import datetime
from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.adapter import BaseAdapter
from app.integrations.schemas import Calendar, CalendarEvent


class BaseCalendarAdapter(BaseAdapter, ABC):
    @abstractmethod
    async def list_calendars(self) -> list[Calendar]:
        pass

    @abstractmethod
    async def get_calendar(self, calendar_id: str) -> Calendar:
        pass

    @abstractmethod
    async def list_events(
        self,
        calendar_id: str,
        start_date: datetime.datetime | None = None,
        end_date: datetime.datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def get_event(self, calendar_id: str, event_id: str) -> CalendarEvent:
        pass

    @abstractmethod
    async def get_free_busy(
        self,
        calendar_ids: list[str],
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        timezone: str | None = None,
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_date: datetime.datetime | None = None,
        end_date: datetime.datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[CalendarEvent]:
        pass

    @abstractmethod
    async def get_user_info(self) -> dict[str, Any]:
        pass

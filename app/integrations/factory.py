import uuid
from collections.abc import Callable

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import AsyncSessionLocal
from app.integrations.adapters.gcs.adapter import GCSAdapter
from app.integrations.adapters.google_calendar.adapter import GoogleCalendarAdapter
from app.integrations.adapters.salesforce.adapter import SalesforceAdapter
from app.integrations.adapters.slack.adapter import SlackAdapter
from app.integrations.backends.calendar.backend import CalendarBackend
from app.integrations.backends.crm.backend import CRMBackend
from app.integrations.backends.file.backend import FileBackend
from app.integrations.backends.messaging.backend import MessagingBackend
from app.integrations.base.adapter import BaseAdapter
from app.integrations.base.backend import BaseBackend
from app.integrations.base.calendar_backend import BaseCalendarBackend
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.base.file_backend import BaseFileBackend
from app.integrations.base.messaging_backend import BaseMessagingBackend
from app.integrations.context import IntegrationContext, create_context
from app.integrations.handles import (
    <PERSON><PERSON><PERSON><PERSON>,
    CRMHandle,
    FileHandle,
    MessagingHandle,
)
from app.integrations.types import BackendType, IntegrationSource


class IntegrationFactory:
    def __init__(self, context: IntegrationContext):
        self.context = context

    def crm(self, source: IntegrationSource) -> CRMHandle:
        backend = self._create_backend(source, BackendType.CRM)

        if not isinstance(backend, BaseCRMBackend):
            raise TypeError("Created backend does not implement BaseCRMBackend")

        return CRMHandle(backend)

    def messaging(self, source: IntegrationSource) -> MessagingHandle:
        backend = self._create_backend(source, BackendType.MESSAGING)

        if not isinstance(backend, BaseMessagingBackend):
            raise TypeError("Created backend does not implement BaseMessagingBackend")

        return MessagingHandle(backend)

    def file(self, source: IntegrationSource) -> FileHandle:
        backend = self._create_backend(source, BackendType.FILE)

        if not isinstance(backend, BaseFileBackend):
            raise TypeError("Created backend does not implement BaseFileBackend")

        return FileHandle(backend)

    def calendar(self, source: IntegrationSource) -> CalendarHandle:
        backend = self._create_backend(source, BackendType.CALENDAR)

        if not isinstance(backend, BaseCalendarBackend):
            raise TypeError("Created backend does not implement BaseCalendarBackend")

        return CalendarHandle(backend)

    def _create_backend(
        self,
        source: IntegrationSource,
        type_: BackendType,
    ) -> BaseBackend:
        mapping = self._get_integration_mapping()

        if source not in mapping:
            raise ValueError(f"Unsupported source: {source}")

        if type_ not in mapping[source]:
            raise ValueError(f"Unsupported backend type {type_} for source {source}")

        adapter_class, backend_class = mapping[source][type_]

        return backend_class(
            context=self.context, adapter_class=adapter_class, source=source
        )

    @staticmethod
    def _get_integration_mapping() -> dict[
        IntegrationSource,
        dict[BackendType, tuple[type[BaseAdapter], type[BaseBackend]]],
    ]:
        return {
            IntegrationSource.SALESFORCE: {
                BackendType.CRM: (SalesforceAdapter, CRMBackend),
            },
            IntegrationSource.SLACK: {
                BackendType.MESSAGING: (SlackAdapter, MessagingBackend),
            },
            IntegrationSource.GCS: {
                BackendType.FILE: (GCSAdapter, FileBackend),
            },
            IntegrationSource.GOOGLE_CALENDAR: {
                BackendType.CALENDAR: (GoogleCalendarAdapter, CalendarBackend),
            },
        }


def create_factory(
    tenant_id: uuid.UUID,
    credentials_resolver: ICredentialsResolver | None = None,
    db_session: AsyncSession | None = None,
    db_session_factory: Callable[[], AsyncSession] | None = None,
) -> IntegrationFactory:
    if db_session is None and db_session_factory is None:
        db_session_factory = AsyncSessionLocal

    context = create_context(
        tenant_id=tenant_id,
        credentials_resolver=credentials_resolver,
        db_session=db_session,
        db_session_factory=db_session_factory,
    )
    return IntegrationFactory(context)

from collections.abc import Callable
from contextlib import asynccontextmanager
from typing import Any, cast

from app.integrations.backends.crm.bulk_access_sync_handler import (
    BulkAccountAccessSyncHandler,
)
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.context import IntegrationContext
from app.integrations.stores.pg_crm_store import PostgresCRMStore
from app.integrations.types import IntegrationSource


class CRMBackend(BaseCRMBackend):
    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseCRMAdapter],
        source: IntegrationSource,
    ):
        super().__init__(
            context=context,
            adapter_class=adapter_class,
            source=source,
        )

    @asynccontextmanager
    async def _get_crm_store(self):
        async with self.context.db_session_factory() as session:
            crm_store = PostgresCRMStore(
                tenant_id=self.context.tenant_id,
                source=self.source,
                session=session,
            )
            yield crm_store

    async def get_adapter(self) -> BaseCRMAdapter:
        if not hasattr(self, "_adapter"):
            credentials = await self._context.credentials_resolver.get_credentials(
                self.source
            )
            self._adapter = self._adapter_class(credentials=credentials)
        return self._adapter

    # Account Access Management
    async def list_account_access(
        self, crm_user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        async with self._get_crm_store() as crm_store:
            access_slice = await crm_store.get_user_account_access(crm_user_id)
            paginated_accounts = access_slice.accounts[offset : offset + limit]
            accounts = []
            for account_access in paginated_accounts:
                accounts.append(
                    {
                        "Id": account_access.account_id,
                        "Name": account_access.account_name,
                        "AccessType": account_access.access_type,
                        "AccessRole": account_access.access_role or "",
                    }
                )

            return accounts

    async def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        ctx = cast("IntegrationContext", self.context)
        credentials_resolver = cast("ICredentialsResolver", ctx.credentials_resolver)

        async with self._get_crm_store() as crm_store:
            handler = BulkAccountAccessSyncHandler(
                tenant_id=self.context.tenant_id,
                source=self.source,
                credentials_resolver=credentials_resolver,
                db_session_factory=ctx.db_session_factory,
                adapter_factory=lambda credentials: self.adapter_class(
                    credentials=credentials
                ),
                crm_store=crm_store,
            )
            return await handler.execute(
                crm_user_ids=crm_user_ids,
                get_credentials_resolver=get_credentials_resolver,
                interval_seconds=interval_seconds,
                daemon_mode=daemon_mode,
            )

    # Opportunity Management
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.get_opportunity(opportunity_id)

    async def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.update_opportunity(opportunity_id, fields)

    async def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.list_opportunities_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.search_opportunities(
            search_criteria=search_criteria, limit=limit, offset=offset
        )

    # Account Management
    async def get_account(self, account_id: str) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.get_account(account_id)

    async def update_account(
        self, account_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.update_account(account_id, fields)

    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.search_accounts(
            search_criteria=search_criteria, limit=limit, offset=offset
        )

    # Contact Management
    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.get_contact(contact_id)

    async def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.create_contact(contact_data)

    async def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.update_contact(contact_id, contact_data)

    async def list_contacts_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.list_contacts_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.search_contacts(
            search_criteria=search_criteria, limit=limit, offset=offset
        )

    # Task Management
    async def get_task(self, task_id: str) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.get_task(task_id)

    async def create_task(self, task_data: dict[str, Any]) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.create_task(task_data)

    async def update_task(
        self, task_id: str, task_data: dict[str, Any]
    ) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.update_task(task_id, task_data)

    async def list_tasks_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.list_tasks_by_contact(
            contact_id=contact_id, limit=limit, offset=offset
        )

    async def list_tasks_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.list_tasks_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def list_tasks_by_opportunity(
        self,
        opportunity_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.list_tasks_by_opportunity(
            opportunity_id=opportunity_id, limit=limit, offset=offset
        )

    # Event Management
    async def get_event(self, event_id: str) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.get_event(event_id)

    async def create_event(self, event_data: dict[str, Any]) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.create_event(event_data)

    async def update_event(
        self, event_id: str, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        adapter = await self.get_adapter()
        return await adapter.update_event(event_id, event_data)

    async def list_events_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.list_events_by_contact(
            contact_id=contact_id, limit=limit, offset=offset
        )

    async def list_events_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.list_events_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def list_events_by_opportunity(
        self,
        opportunity_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        adapter = await self.get_adapter()
        return await adapter.list_events_by_opportunity(
            opportunity_id=opportunity_id, limit=limit, offset=offset
        )

from collections.abc import Callable
from datetime import datetime
from typing import Any

from app.integrations.base.calendar_backend import BaseCalendarBackend
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.base.file_backend import BaseFileBackend
from app.integrations.base.messaging_backend import BaseMessagingBackend
from app.integrations.schemas import DocumentData
from app.integrations.types import ExtensionType, IntegrationSource


class BaseHandle:
    def __init__(self, backend):
        self._backend = backend

    @property
    def source(self) -> IntegrationSource:
        return self._backend.source


class CRMHandle(BaseHandle):
    def __init__(self, backend: BaseCRMBackend):
        super().__init__(backend)

    # Core CRM operations
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return await self._backend.get_opportunity(opportunity_id)

    async def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_opportunity(opportunity_id, fields)

    async def list_opportunities_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_opportunities_by_account(
            account_id, limit, offset
        )

    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._backend.search_opportunities(search_criteria, limit, offset)

    async def get_account(self, account_id: str) -> dict[str, Any]:
        return await self._backend.get_account(account_id)

    async def update_account(
        self, account_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_account(account_id, fields)

    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._backend.search_accounts(search_criteria, limit, offset)

    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        return await self._backend.get_contact(contact_id)

    async def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        return await self._backend.create_contact(contact_data)

    async def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_contact(contact_id, contact_data)

    async def list_contacts_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_contacts_by_account(account_id, limit, offset)

    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._backend.search_contacts(search_criteria, limit, offset)

    async def get_task(self, task_id: str) -> dict[str, Any]:
        return await self._backend.get_task(task_id)

    async def create_task(self, task_data: dict[str, Any]) -> dict[str, Any]:
        return await self._backend.create_task(task_data)

    async def update_task(
        self, task_id: str, task_data: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_task(task_id, task_data)

    async def list_tasks_by_contact(
        self, contact_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_tasks_by_contact(contact_id, limit, offset)

    async def list_tasks_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_tasks_by_account(account_id, limit, offset)

    async def list_tasks_by_opportunity(
        self, opportunity_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_tasks_by_opportunity(
            opportunity_id, limit, offset
        )

    async def get_event(self, event_id: str) -> dict[str, Any]:
        return await self._backend.get_event(event_id)

    async def create_event(self, event_data: dict[str, Any]) -> dict[str, Any]:
        return await self._backend.create_event(event_data)

    async def update_event(
        self, event_id: str, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_event(event_id, event_data)

    async def list_events_by_contact(
        self, contact_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_events_by_contact(contact_id, limit, offset)

    async def list_events_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_events_by_account(account_id, limit, offset)

    async def list_events_by_opportunity(
        self, opportunity_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_events_by_opportunity(
            opportunity_id, limit, offset
        )

    async def list_account_access(
        self, crm_user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_account_access(crm_user_id, limit, offset)

    # Sync operations
    async def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        return await self._backend.bulk_sync_account_access(
            crm_user_ids, get_credentials_resolver, interval_seconds, daemon_mode
        )


class MessagingHandle(BaseHandle):
    def __init__(self, backend: BaseMessagingBackend):
        super().__init__(backend)

    async def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        return await self._backend.search_channel_messages(channel_id, query, limit)

    async def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        return await self._backend.start_channel_ingestion(
            channel_ids,
            interval_seconds,
            lookback_days,
            batch_size,
            daemon_mode,
        )

    async def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        return await self._backend.start_channel_processing(
            channel_ids, interval_seconds, batch_size, daemon_mode
        )


class FileHandle(BaseHandle):
    def __init__(self, backend: BaseFileBackend):
        super().__init__(backend)

    async def start_processing(
        self, bucket_names: list[str], extensions: list[ExtensionType]
    ) -> dict[str, Any]:
        return await self._backend.start_processing(bucket_names, extensions)

    async def search_files(
        self,
        query: str,
        limit: int = 10,
    ) -> list[tuple[DocumentData, float]]:
        return await self._backend.search_files(query, limit)


class CalendarHandle(BaseHandle):
    def __init__(self, backend: BaseCalendarBackend):
        super().__init__(backend)

    async def list_calendars(self) -> list[dict[str, Any]]:
        return await self._backend.list_calendars()

    async def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        return await self._backend.get_calendar(calendar_id)

    async def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        return await self._backend.get_event(calendar_id, event_id)

    async def list_events(
        self,
        calendar_id: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        return await self._backend.list_events(
            calendar_id,
            start_time,
            end_time,
            max_results,
            single_events,
            order_by,
            show_deleted,
            page_token,
        )

    async def get_free_busy(
        self,
        calendar_ids: list[str],
        start_time: datetime,
        end_time: datetime,
        timezone: str | None = None,
    ) -> dict[str, Any]:
        return await self._backend.get_free_busy(
            calendar_ids, start_time, end_time, timezone
        )

    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[dict[str, Any]]:
        return await self._backend.search_events(
            calendar_id, query, start_time, end_time, max_results, order_by
        )

    async def get_user_info(self) -> dict[str, Any]:
        return await self._backend.get_user_info()

from datetime import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.adapters.google_calendar.client import GoogleCalendarClient
from app.integrations.adapters.google_calendar.utils import (
    convert_google_calendar_to_calendar,
    convert_google_event_to_calendar_event,
)
from app.integrations.base.calendar_adapter import BaseCalendarAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.schemas import Calendar, CalendarEvent
from app.integrations.types import IntegrationSource

logger = get_logger()


class GoogleCalendarAdapter(BaseCalendarAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._client = self._create_client(credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.GOOGLE_CALENDAR

    def _create_client(self, credentials: ICredentials) -> GoogleCalendarClient:
        required_fields = [
            "client_id",
            "client_secret",
            "access_token",
            "refresh_token",
            "scope",
        ]

        for field in required_fields:
            if field not in credentials.secrets:
                error_msg = f"Google Calendar {field} not found in credentials"
                logger.error(error_msg)
                raise ValueError(error_msg)

        return GoogleCalendarClient(credentials=credentials.secrets)

    async def list_calendars(self) -> list[Calendar]:
        response = await self._client.list_calendars()
        calendars = []

        for calendar_data in response.get("items", []):
            calendar = convert_google_calendar_to_calendar(calendar_data)
            calendars.append(calendar)

        return calendars

    async def get_calendar(self, calendar_id: str) -> Calendar:
        calendar_data = await self._client.get_calendar(calendar_id)
        return convert_google_calendar_to_calendar(calendar_data)

    async def list_events(
        self,
        calendar_id: str,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        time_min = start_date.isoformat() if start_date else None
        time_max = end_date.isoformat() if end_date else None

        response = await self._client.list_events(
            calendar_id=calendar_id,
            time_min=time_min,
            time_max=time_max,
            max_results=max_results,
            single_events=single_events,
            order_by=order_by,
            show_deleted=show_deleted,
            page_token=page_token,
        )

        events = []
        for event_data in response.get("items", []):
            event = convert_google_event_to_calendar_event(event_data)
            event.calendar_id = calendar_id
            events.append(event)

        return {
            "events": events,
            "next_page_token": response.get("nextPageToken"),
        }

    async def get_event(self, calendar_id: str, event_id: str) -> CalendarEvent:
        event_data = await self._client.get_event(calendar_id, event_id)
        event = convert_google_event_to_calendar_event(event_data)
        event.calendar_id = calendar_id
        return event

    async def get_free_busy(
        self,
        calendar_ids: list[str],
        start_time: datetime,
        end_time: datetime,
        timezone: str | None = None,
    ) -> dict[str, Any]:
        time_min = start_time.isoformat()
        time_max = end_time.isoformat()

        response = await self._client.get_free_busy(
            calendar_ids=calendar_ids,
            time_min=time_min,
            time_max=time_max,
            timezone=timezone,
        )

        calendars = []
        for cal_id in calendar_ids:
            cal_data = response.get("calendars", {}).get(cal_id, {})
            busy_periods = []

            for busy in cal_data.get("busy", []):
                start_dt = datetime.fromisoformat(busy["start"].replace("Z", "+00:00"))
                end_dt = datetime.fromisoformat(busy["end"].replace("Z", "+00:00"))
                busy_periods.append({"start": start_dt, "end": end_dt})

            calendars.append(
                {
                    "calendar_id": cal_id,
                    "busy_periods": busy_periods,
                    "errors": cal_data.get("errors", []),
                }
            )

        return {
            "calendars": calendars,
            "start_time": start_time,
            "end_time": end_time,
        }

    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[CalendarEvent]:
        events_response = await self.list_events(
            calendar_id=calendar_id,
            start_date=start_date,
            end_date=end_date,
            max_results=max_results,
            order_by=order_by,
        )

        events = events_response["events"]
        query_lower = query.lower()

        filtered_events = []
        for event in events:
            if (
                query_lower in event.title.lower()
                or (event.description and query_lower in event.description.lower())
                or (event.location and query_lower in event.location.lower())
            ):
                filtered_events.append(event)

        return filtered_events

    async def get_user_info(self) -> dict[str, Any]:
        return await self._client.get_user_info()

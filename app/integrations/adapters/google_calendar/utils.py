import datetime
from typing import Any

from app.integrations.schemas import (
    Calendar,
    CalendarEvent,
    CalendarEventAttendee,
    CalendarEventDateTime,
    CalendarEventRecurrence,
)

FREQUENCY_MAP = {
    "daily": "DAILY",
    "weekly": "WEEKLY",
    "monthly": "MONTHLY",
    "yearly": "YEARLY",
}


def parse_google_datetime(
    dt_data: dict[str, Any] | None,
) -> CalendarEventDateTime | None:
    if not dt_data:
        return None

    dt_str = dt_data.get("dateTime")
    date_str = dt_data.get("date")
    timezone = dt_data.get("timeZone")

    if dt_str:
        dt = datetime.datetime.fromisoformat(dt_str.replace("Z", "+00:00"))
        return CalendarEventDateTime(date_time=dt, timezone=timezone)
    elif date_str:
        date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
        return CalendarEventDateTime(day=date, timezone=timezone)

    return None


def parse_google_attendee(attendee_data: dict[str, Any]) -> CalendarEventAttendee:
    return CalendarEventAttendee(
        email=attendee_data.get("email", ""),
        name=attendee_data.get("displayName"),
        response_status=attendee_data.get("responseStatus"),
        is_organizer=attendee_data.get("organizer", False),
        is_optional=attendee_data.get("optional", False),
    )


def parse_google_recurrence(
    recurrence_data: list[str] | None,
) -> CalendarEventRecurrence | None:
    if not recurrence_data:
        return None

    rrule = recurrence_data[0] if recurrence_data else ""

    if not rrule.startswith("RRULE:"):
        return None

    parts = rrule[6:].split(";")
    rule_dict = {}

    for part in parts:
        if "=" in part:
            key, value = part.split("=", 1)
            rule_dict[key] = value

    frequency = FREQUENCY_MAP.get(rule_dict.get("FREQ", ""), "daily")
    interval = int(rule_dict.get("INTERVAL", 1))
    count = int(rule_dict["COUNT"]) if "COUNT" in rule_dict else None

    until = None
    if "UNTIL" in rule_dict:
        until_str = rule_dict["UNTIL"]
        try:
            until = datetime.datetime.strptime(until_str, "%Y%m%dT%H%M%SZ")
        except ValueError:
            try:
                until = datetime.datetime.strptime(until_str, "%Y%m%d")
            except ValueError:
                pass

    by_day = None
    if "BYDAY" in rule_dict:
        by_day = rule_dict["BYDAY"].split(",")

    return CalendarEventRecurrence(
        frequency=frequency,
        interval=interval,
        count=count,
        until=until,
        by_day=by_day,
    )


def convert_google_event_to_calendar_event(event_data: dict[str, Any]) -> CalendarEvent:
    start = parse_google_datetime(event_data.get("start"))
    end = parse_google_datetime(event_data.get("end"))

    attendees = []
    if "attendees" in event_data:
        attendees = [parse_google_attendee(att) for att in event_data["attendees"]]

    organizer = None
    if "organizer" in event_data:
        organizer = parse_google_attendee(event_data["organizer"])

    recurrence = parse_google_recurrence(event_data.get("recurrence"))

    all_day = (
        True if start and start.day is not None and start.date_time is None else False
    )

    created_at = None
    if "created" in event_data:
        created_at = datetime.datetime.fromisoformat(
            event_data["created"].replace("Z", "+00:00")
        )

    updated_at = None
    if "updated" in event_data:
        updated_at = datetime.datetime.fromisoformat(
            event_data["updated"].replace("Z", "+00:00")
        )

    return CalendarEvent(
        id=event_data["id"],
        calendar_id=event_data.get("organizer", {}).get("email", ""),
        title=event_data.get("summary", ""),
        description=event_data.get("description"),
        location=event_data.get("location"),
        start=start,
        end=end,
        all_day=all_day,
        attendees=attendees,
        organizer=organizer,
        recurrence=recurrence,
        status=event_data.get("status", "confirmed"),
        visibility=event_data.get("visibility", "default"),
        created_at=created_at,
        updated_at=updated_at,
        html_link=event_data.get("htmlLink"),
        meeting_url=event_data.get("hangoutLink"),
    )


def convert_google_calendar_to_calendar(calendar_data: dict[str, Any]) -> Calendar:
    return Calendar(
        id=calendar_data["id"],
        name=calendar_data.get("summary", ""),
        description=calendar_data.get("description"),
        timezone=calendar_data.get("timeZone"),
        is_primary=calendar_data.get("primary", False),
        access_role=calendar_data.get("accessRole", "reader"),
        color_id=calendar_data.get("colorId"),
        background_color=calendar_data.get("backgroundColor"),
        foreground_color=calendar_data.get("foregroundColor"),
    )

from typing import Any

from google.auth.credentials import Credentials
from google.oauth2.credentials import Credentials as OAuth2Credentials
from googleapiclient.discovery import build

from app.common.helpers.logger import get_logger
from app.common.helpers.to_async import to_async

logger = get_logger()


class GoogleCalendarClientError(Exception):
    pass


class GoogleCalendarClient:
    def __init__(self, credentials: dict[str, Any]):
        self._credentials = self._create_credentials(credentials)
        self._service = self._build_service()

    def _create_credentials(self, credentials: dict[str, Any]) -> Credentials:
        try:
            return OAuth2Credentials(
                token=credentials.get("access_token"),
                refresh_token=credentials.get("refresh_token"),
                token_uri=credentials.get(
                    "token_uri", "https://oauth2.googleapis.com/token"
                ),
                client_id=credentials.get("client_id"),
                client_secret=credentials.get("client_secret"),
                scopes=credentials.get("scope", "").split(" "),
            )
        except Exception as e:
            raise GoogleCalendarClientError(
                f"Failed to create credentials: {str(e)}"
            ) from e

    def _build_service(self):
        try:
            return build("calendar", "v3", credentials=self._credentials)
        except Exception as e:
            raise GoogleCalendarClientError(
                f"Failed to build Calendar service: {str(e)}"
            ) from e

    @to_async
    def list_calendars(self) -> dict[str, Any]:
        try:
            return self._service.calendarList().list().execute()
        except Exception as e:
            logger.exception("Failed to list calendars")
            raise GoogleCalendarClientError(
                f"Failed to list calendars: {str(e)}"
            ) from e

    @to_async
    def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        try:
            return self._service.calendarList().get(calendarId=calendar_id).execute()
        except Exception as e:
            logger.exception(f"Failed to get calendar {calendar_id}")
            raise GoogleCalendarClientError(f"Failed to get calendar: {str(e)}") from e

    @to_async
    def list_events(
        self,
        calendar_id: str,
        time_min: str | None = None,
        time_max: str | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        try:
            params = {
                "calendarId": calendar_id,
                "maxResults": max_results,
                "singleEvents": single_events,
                "orderBy": order_by,
                "showDeleted": show_deleted,
            }

            if time_min:
                params["timeMin"] = time_min
            if time_max:
                params["timeMax"] = time_max
            if page_token:
                params["pageToken"] = page_token

            return self._service.events().list(**params).execute()
        except Exception as e:
            logger.exception(f"Failed to list events for calendar {calendar_id}")
            raise GoogleCalendarClientError(f"Failed to list events: {str(e)}") from e

    @to_async
    def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        try:
            return (
                self._service.events()
                .get(calendarId=calendar_id, eventId=event_id)
                .execute()
            )
        except Exception as e:
            logger.exception(
                f"Failed to get event {event_id} from calendar {calendar_id}"
            )
            raise GoogleCalendarClientError(f"Failed to get event: {str(e)}") from e

    @to_async
    def create_event(
        self,
        calendar_id: str,
        event_data: dict[str, Any],
        send_notifications: bool = True,
    ) -> dict[str, Any]:
        try:
            return (
                self._service.events()
                .insert(
                    calendarId=calendar_id,
                    body=event_data,
                    sendNotifications=send_notifications,
                )
                .execute()
            )
        except Exception as e:
            logger.exception(f"Failed to create event in calendar {calendar_id}")
            raise GoogleCalendarClientError(f"Failed to create event: {str(e)}") from e

    @to_async
    def update_event(
        self,
        calendar_id: str,
        event_id: str,
        event_data: dict[str, Any],
        send_notifications: bool = True,
    ) -> dict[str, Any]:
        try:
            return (
                self._service.events()
                .update(
                    calendarId=calendar_id,
                    eventId=event_id,
                    body=event_data,
                    sendNotifications=send_notifications,
                )
                .execute()
            )
        except Exception as e:
            logger.exception(
                f"Failed to update event {event_id} in calendar {calendar_id}"
            )
            raise GoogleCalendarClientError(f"Failed to update event: {str(e)}") from e

    @to_async
    def delete_event(
        self,
        calendar_id: str,
        event_id: str,
        send_notifications: bool = True,
    ) -> None:
        try:
            self._service.events().delete(
                calendarId=calendar_id,
                eventId=event_id,
                sendNotifications=send_notifications,
            ).execute()
        except Exception as e:
            logger.exception(
                f"Failed to delete event {event_id} from calendar {calendar_id}"
            )
            raise GoogleCalendarClientError(f"Failed to delete event: {str(e)}") from e

    @to_async
    def get_free_busy(
        self,
        calendar_ids: list[str],
        time_min: str,
        time_max: str,
        timezone: str | None = None,
    ) -> dict[str, Any]:
        try:
            body = {
                "timeMin": time_min,
                "timeMax": time_max,
                "items": [{"id": cal_id} for cal_id in calendar_ids],
            }

            if timezone:
                body["timeZone"] = timezone

            return self._service.freebusy().query(body=body).execute()
        except Exception as e:
            logger.exception(
                f"Failed to get free/busy information for calendars {calendar_ids}"
            )
            raise GoogleCalendarClientError(
                f"Failed to get free/busy information: {str(e)}"
            ) from e

    @to_async
    def get_user_info(self) -> dict[str, Any]:
        try:
            return self._service.calendarList().get(calendarId="primary").execute()
        except Exception as e:
            logger.exception("Failed to get user info")
            raise GoogleCalendarClientError(f"Failed to get user info: {str(e)}") from e

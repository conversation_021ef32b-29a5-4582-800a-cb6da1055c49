from enum import Enum
from typing import Any, Literal

from app.common.helpers.logger import get_logger
from app.integrations.adapters.salesforce.access_resolver import (
    SalesforceAccountAccessResolver,
)
from app.integrations.adapters.salesforce.refreshable_client_mixin import (
    SalesforceRefreshableClientMixin,
)
from app.integrations.base.credentials_resolver import (
    ICredentials,
)
from app.integrations.schemas import CRMAccountAccessData

logger = get_logger()


class SalesforceObjectType(str, Enum):
    ACCOUNT = "Account"
    OPPORTUNITY = "Opportunity"
    CONTACT = "Contact"
    TASK = "Task"
    EVENT = "Event"


class SalesforceHandler(SalesforceRefreshableClientMixin):
    def __init__(self, credentials: ICredentials):
        self.init_salesforce_client(credentials)

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return await self.salesforce_client.get_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        await self.salesforce_client.update_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id, fields
        )

        updated_opportunity = await self.salesforce_client.get_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id
        )

        return updated_opportunity

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def list_opportunities_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        where_clause = f"AccountId = '{account_id}'"

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.OPPORTUNITY.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        where_conditions = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["name", "stagename"]:
                    where_conditions.append(f"{field} LIKE '%{value}%'")
                elif field.lower() in ["amount", "probability"]:
                    where_conditions.append(f"{field} = {value}")
                elif field.lower() in ["closedate"]:
                    where_conditions.append(f"{field} = {value}")
                else:
                    where_conditions.append(f"{field} = '{value}'")

        if not where_conditions:
            return []

        where_clause = " AND ".join(where_conditions)

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.OPPORTUNITY.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def get_event(self, event_id: str) -> dict[str, Any]:
        return await self.salesforce_client.get_object(
            SalesforceObjectType.EVENT.value, event_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def create_event(self, event_data: dict[str, Any]) -> dict[str, Any]:
        result = await self.salesforce_client.create_object(
            SalesforceObjectType.EVENT.value, event_data
        )

        if result.get("success") and result.get("id"):
            return await self.get_event(result["id"])

        return result

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def update_event(
        self, event_id: str, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        await self.salesforce_client.update_object(
            SalesforceObjectType.EVENT.value, event_id, event_data
        )

        updated_event = await self.salesforce_client.get_object(
            SalesforceObjectType.EVENT.value, event_id
        )

        return updated_event

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def list_events_by_contact(
        self,
        contact_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not contact_id:
            return []

        where_clause = f"WhoId = '{contact_id}'"

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.EVENT.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def list_events_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        where_clause = f"WhatId = '{account_id}'"

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.EVENT.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def list_events_by_opportunity(
        self,
        opportunity_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not opportunity_id:
            return []

        where_clause = f"WhatId = '{opportunity_id}'"

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.EVENT.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def get_task(self, task_id: str) -> dict[str, Any]:
        return await self.salesforce_client.get_object(
            SalesforceObjectType.TASK.value, task_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def create_task(self, task_data: dict[str, Any]) -> dict[str, Any]:
        result = await self.salesforce_client.create_object(
            SalesforceObjectType.TASK.value, task_data
        )

        if result.get("success") and result.get("id"):
            return await self.get_task(result["id"])

        return result

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def update_task(
        self, task_id: str, task_data: dict[str, Any]
    ) -> dict[str, Any]:
        await self.salesforce_client.update_object(
            SalesforceObjectType.TASK.value, task_id, task_data
        )

        updated_task = await self.salesforce_client.get_object(
            SalesforceObjectType.TASK.value, task_id
        )

        return updated_task

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def list_tasks_by_contact(
        self,
        contact_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not contact_id:
            return []

        where_clause = f"WhoId = '{contact_id}'"

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.TASK.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def list_tasks_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        where_clause = f"WhatId = '{account_id}'"

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.TASK.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def list_tasks_by_opportunity(
        self,
        opportunity_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not opportunity_id:
            return []

        where_clause = f"WhatId = '{opportunity_id}'"

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.TASK.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def get_account(self, account_id: str) -> dict[str, Any]:
        return await self.salesforce_client.get_object(
            SalesforceObjectType.ACCOUNT.value, account_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def update_account(
        self, account_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        await self.salesforce_client.update_object(
            SalesforceObjectType.ACCOUNT.value, account_id, fields
        )

        updated_account = await self.salesforce_client.get_object(
            SalesforceObjectType.ACCOUNT.value, account_id
        )

        return updated_account

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        where_conditions = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["name", "industry", "type"]:
                    where_conditions.append(f"{field} LIKE '%{value}%'")
                elif field.lower() in ["phone", "website"]:
                    where_conditions.append(f"{field} = '{value}'")
                elif field.lower() in ["annualrevenue", "numberofemployees"]:
                    where_conditions.append(f"{field} = {value}")
                else:
                    where_conditions.append(f"{field} = '{value}'")

        if not where_conditions:
            return []

        where_clause = " AND ".join(where_conditions)

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.ACCOUNT.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def resolve_account_access(
        self, salesforce_user_id: str
    ) -> list[CRMAccountAccessData]:
        resolver = SalesforceAccountAccessResolver(client=self.salesforce_client)
        return await resolver.get_user_account_access(salesforce_user_id)

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        return await self.salesforce_client.get_object(
            SalesforceObjectType.CONTACT.value, contact_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        result = await self.salesforce_client.create_object(
            SalesforceObjectType.CONTACT.value, contact_data
        )

        if result.get("success") and result.get("id"):
            return await self.get_contact(result["id"])

        return result

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        await self.salesforce_client.update_object(
            SalesforceObjectType.CONTACT.value, contact_id, contact_data
        )

        updated_contact = await self.salesforce_client.get_object(
            SalesforceObjectType.CONTACT.value, contact_id
        )

        return updated_contact

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def list_contacts_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        where_clause = f"AccountId = '{account_id}'"

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.CONTACT.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        where_conditions = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["firstname", "lastname", "name"]:
                    where_conditions.append(f"{field} LIKE '%{value}%'")
                elif field.lower() == "email":
                    where_conditions.append(f"Email = '{value}'")
                elif field.lower() == "phone":
                    where_conditions.append(f"Phone = '{value}'")
                else:
                    where_conditions.append(f"{field} = '{value}'")

        if not where_conditions:
            return []

        where_clause = " AND ".join(where_conditions)

        return await self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.CONTACT.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

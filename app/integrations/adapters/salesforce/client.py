from typing import Any, Literal

from simple_salesforce import Salesforce, SalesforceAuthenticationFailed, format_soql

from app.common.helpers.logger import get_logger
from app.common.helpers.to_async import to_async

logger = get_logger()


class SalesforceClientError(Exception):
    pass


class SalesforceClient:
    def __init__(
        self,
        username: str | None = None,
        password: str | None = None,
        security_token: str | None = None,
        instance_url: str | None = None,
        access_token: str | None = None,
        domain: str = "login",
        version: str = "55.0",
    ):
        self.username = username
        self.password = password
        self.security_token = security_token
        self.instance_url = instance_url
        self.access_token = access_token
        self.domain = domain
        self.version = version

    def _create_client(self) -> Salesforce:
        try:
            if self.access_token:
                logger.debug("Creating Salesforce client with access token")
                return Salesforce(
                    instance_url=self.instance_url,
                    session_id=self.access_token,
                    domain=self.domain,
                    version=self.version,
                )
            else:
                logger.debug("Creating Salesforce client with username")
                return Salesforce(
                    username=self.username,
                    password=self.password,
                    security_token=self.security_token,
                    domain=self.domain,
                    version=self.version,
                )

        except SalesforceAuthenticationFailed as e:
            logger.exception("Salesforce authentication failed")
            raise SalesforceClientError(f"Authentication failed: {str(e)}") from e
        except Exception as e:
            logger.exception("Failed to create Salesforce client")
            raise SalesforceClientError(
                f"Client initialization failed: {str(e)}"
            ) from e

    @property
    def _client(self):
        if not hasattr(self, "_lazy_client"):
            self._lazy_client = self._create_client()
        return self._lazy_client

    @to_async
    def get_available_objects(self):
        if not hasattr(self, "_cached_available_objects"):
            desc = self._client.describe()
            self._cached_available_objects = {s["name"] for s in desc["sobjects"]}
        return self._cached_available_objects

    @to_async
    def get_object(self, object_type: str, object_id: str) -> dict[str, Any]:
        try:
            sf_object = getattr(self._client, object_type)
            result = sf_object.get(object_id)
            return result
        except Exception as e:
            logger.exception(f"Failed to get {object_type} {object_id}")
            raise SalesforceClientError(f"Failed to get {object_type}: {str(e)}") from e

    @to_async
    def query(self, soql_query: str) -> dict[str, Any]:
        try:
            return self._client.query(soql_query)
        except Exception as e:
            logger.exception("Query failed")
            raise SalesforceClientError(f"Query failed: {str(e)}") from e

    @to_async
    def create_object(self, object_type: str, data: dict[str, Any]) -> dict[str, Any]:
        try:
            sf_object = getattr(self._client, object_type)
            result = sf_object.create(data)
            return result
        except Exception as e:
            logger.exception(f"Failed to create {object_type}")
            raise SalesforceClientError(
                f"Failed to create {object_type}: {str(e)}"
            ) from e

    @to_async
    def update_object(
        self, object_type: str, object_id: str, data: dict[str, Any]
    ) -> None:
        try:
            sf_object = getattr(self._client, object_type)
            sf_object.update(object_id, data)
        except Exception as e:
            logger.exception(f"Failed to update {object_type} {object_id}")
            raise SalesforceClientError(
                f"Failed to update {object_type}: {str(e)}"
            ) from e

    @to_async
    def delete_object(self, object_type: str, object_id: str) -> None:
        try:
            sf_object = getattr(self._client, object_type)
            sf_object.delete(object_id)
        except Exception as e:
            logger.exception(f"Failed to delete {object_type} {object_id}")
            raise SalesforceClientError(
                f"Failed to delete {object_type}: {str(e)}"
            ) from e

    @to_async
    def list_objects(
        self,
        object_type: str,
        fields: list[str] | Literal["ALL"] | None = None,
        where_clause: str | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        try:
            # Temporary as the agent is not yet aware of the Salesforce instance's data model it lives in and thus the field names.
            def get_select_clause(
                fields: list[str] | Literal["ALL"] | None, object_type: str
            ):
                if fields == "ALL":
                    return "FIELDS(ALL)"
                elif fields:
                    return ", ".join(fields)
                else:
                    if object_type in ["Event", "Task"]:
                        return "Id, Subject, WhatId, WhoId, OwnerId"

                    return "Id, Name"

            select_clause = get_select_clause(fields, object_type)

            base_query = "SELECT {select_fields:literal} FROM {table_name:literal}"
            params: dict[str, object] = {
                "select_fields": select_clause,
                "table_name": object_type,
            }

            if where_clause:
                base_query += " WHERE {where_condition:literal}"
                params["where_condition"] = where_clause

            if order_by:
                base_query += " ORDER BY {order_by:literal}"
                params["order_by"] = order_by

            base_query += " LIMIT {limit} OFFSET {offset}"
            params["limit"] = limit
            params["offset"] = offset

            query = format_soql(base_query, **params)
            result = self._client.query(query)
            return result.get("records", [])
        except ValueError as e:
            logger.exception("Invalid query parameters")
            raise SalesforceClientError(f"Invalid query parameters: {str(e)}") from e
        except Exception as e:
            logger.exception(f"Failed to list {object_type} objects")
            raise SalesforceClientError(
                f"Failed to list {object_type} objects: {str(e)}"
            ) from e

    async def list_objects_by_owner(
        self,
        object_type: str,
        owner_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        additional_filters: str | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        where_clause = f"OwnerId = '{owner_id}'"
        if additional_filters:
            where_clause += f" AND {additional_filters}"

        return await self.list_objects(
            object_type=object_type,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    async def list_objects_by_ids(
        self,
        object_type: str,
        object_ids: list[str],
        fields: list[str] | Literal["ALL"] | None = None,
    ) -> list[dict[str, Any]]:
        try:
            if not object_ids:
                return []

            formatted_ids = ", ".join(f"'{obj_id}'" for obj_id in object_ids)
            where_clause = f"Id IN ({formatted_ids})"

            return await self.list_objects(
                object_type=object_type,
                fields=fields,
                where_clause=where_clause,
                limit=len(object_ids),
            )
        except Exception as e:
            logger.exception(f"Failed to list {object_type} objects by IDs")
            raise SalesforceClientError(
                f"Failed to list {object_type} objects by IDs: {str(e)}"
            ) from e

    async def list_objects_by_ids_and_owner(
        self,
        object_type: str,
        object_ids: list[str],
        owner_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        additional_filters: str | None = None,
    ) -> list[dict[str, Any]]:
        try:
            if not object_ids:
                return []

            formatted_ids = ", ".join(f"'{obj_id}'" for obj_id in object_ids)
            where_clause = f"Id IN ({formatted_ids}) AND OwnerId = '{owner_id}'"

            if additional_filters:
                where_clause += f" AND {additional_filters}"

            return await self.list_objects(
                object_type=object_type,
                fields=fields,
                where_clause=where_clause,
                limit=len(object_ids),
            )
        except Exception as e:
            logger.exception(f"Failed to list {object_type} objects by IDs and owner")
            raise SalesforceClientError(
                f"Failed to list {object_type} objects by IDs and owner: {str(e)}"
            ) from e

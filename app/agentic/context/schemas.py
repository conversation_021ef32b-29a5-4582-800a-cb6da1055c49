from collections.abc import Awaitable, Callable
from typing import Any

from pydantic import BaseModel, ConfigDict, Field

from app.integrations.types import IntegrationSource


class ToolDefinition(BaseModel):
    name: str
    description: str
    coroutine: Callable[..., Awaitable[Any]]
    args_schema: type[BaseModel]
    requires_human_review: bool = False

    model_config = ConfigDict(arbitrary_types_allowed=True)


class OrgConfig(BaseModel):
    integrations: dict[IntegrationSource, dict]

    model_config = ConfigDict(arbitrary_types_allowed=True)


class GetOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to retrieve."
    )


class UpdateOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to update."
    )
    fields: dict[str, Any] = Field(
        description="Dictionary of fields to update on the opportunity."
    )


class ListOpportunitiesByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list opportunities for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of opportunities to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of opportunities to skip before starting to return results.",
        ge=0,
    )


class SearchOpportunities(BaseModel):
    search_criteria: dict[str, Any] = Field(
        description="Dictionary of search criteria (e.g., {'Name': 'Client', 'StageName': 'Prospecting'})."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of opportunities to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of opportunities to skip before starting to return results.",
        ge=0,
    )


class GetAccount(BaseModel):
    account_id: str = Field(description="The Salesforce ID of the account to retrieve.")


class UpdateAccount(BaseModel):
    account_id: str = Field(description="The Salesforce ID of the account to update.")
    fields: dict[str, Any] = Field(
        description="Dictionary of fields to update on the account."
    )


class SearchAccounts(BaseModel):
    search_criteria: dict[str, Any] = Field(
        description="Dictionary of search criteria (e.g., {'Name': 'Company', 'Industry': 'Technology'})."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of accounts to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of accounts to skip before starting to return results.",
        ge=0,
    )


class GetContact(BaseModel):
    contact_id: str = Field(description="The Salesforce ID of the contact to retrieve.")


class CreateContact(BaseModel):
    contact_data: dict[str, Any] = Field(
        description="Dictionary of fields to create the contact with."
    )


class UpdateContact(BaseModel):
    contact_id: str = Field(description="The Salesforce ID of the contact to update.")
    contact_data: dict[str, Any] = Field(
        description="Dictionary of fields to update on the contact."
    )


class ListContactsByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list contacts for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of contacts to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of contacts to skip before starting to return results.",
        ge=0,
    )


class GetTask(BaseModel):
    task_id: str = Field(description="The Salesforce ID of the task to retrieve.")


class CreateTask(BaseModel):
    task_data: dict[str, Any] = Field(
        description="Dictionary of fields to create the task with."
    )


class UpdateTask(BaseModel):
    task_id: str = Field(description="The Salesforce ID of the task to update.")
    task_data: dict[str, Any] = Field(
        description="Dictionary of fields to update on the task."
    )


class ListTasksByContact(BaseModel):
    contact_id: str = Field(
        description="The Salesforce ID of the contact to list tasks for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of tasks to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of tasks to skip before starting to return results.",
        ge=0,
    )


class GetEvent(BaseModel):
    event_id: str = Field(description="The Salesforce ID of the event to retrieve.")


class CreateEvent(BaseModel):
    event_data: dict[str, Any] = Field(
        description="Dictionary of fields to create the event with."
    )


class UpdateEvent(BaseModel):
    event_id: str = Field(description="The Salesforce ID of the event to update.")
    event_data: dict[str, Any] = Field(
        description="Dictionary of fields to update on the event."
    )


class ListEventsByContact(BaseModel):
    contact_id: str = Field(
        description="The Salesforce ID of the contact to list events for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of events to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of events to skip before starting to return results.",
        ge=0,
    )


class GetCurrentUserTerritory(BaseModel):
    """Get the current user's sales territory - no parameters needed"""

    pass


class ListEventsByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list events for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of events to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of events to skip before starting to return results.",
        ge=0,
    )


class ListEventsByOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to list events for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of events to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of events to skip before starting to return results.",
        ge=0,
    )


class ListTasksByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list tasks for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of tasks to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of tasks to skip before starting to return results.",
        ge=0,
    )


class ListTasksByOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to list tasks for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of tasks to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of tasks to skip before starting to return results.",
        ge=0,
    )


class SearchContacts(BaseModel):
    search_criteria: dict[str, Any] = Field(
        description="Dictionary of search criteria (e.g., {'Email': '<EMAIL>', 'FirstName': 'John'})."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of contacts to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of contacts to skip before starting to return results.",
        ge=0,
    )


class ListAccountAccess(BaseModel):
    crm_user_id: str = Field(
        description="The Salesforce ID of the user to list account access for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of account access records to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of account access records to skip before starting to return results.",
        ge=0,
    )


class RetrieveDocuments(BaseModel):
    query: str = Field(description="The query to retrieve documents from the database.")
